{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Untitled Notebook\n", "\n", "This is an initial placeholder notebook. Feel free to edit and rename as well as create your own notebooks, to use Google Cloud Datalab."]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false}, "outputs": [], "source": ["# Code cell ready to be run...\n", "print('Hello!')"]}], "metadata": {"kernelspec": {"display_name": "Python 2", "language": "python", "name": "python2"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.9"}}, "nbformat": 4, "nbformat_minor": 0}