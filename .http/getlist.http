POST /data/dataSet/findPageBySearch HTTP/1.1
Accept: application/json, text/javascript, */*; q=0.01
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9,en;q=0.8
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 218
Content-Type: application/x-www-form-urlencoded; charset=UTF-8
Host: opendata.sz.gov.cn
Origin: https://opendata.sz.gov.cn
Pragma: no-cache
Referer: https://opendata.sz.gov.cn/data/search/toSearchPost
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
X-Requested-With: XMLHttpRequest
sec-ch-ua: "Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "macOS"

{
  "pageNo": 1,
  "pageSize": 9,
  "matters": "",
  "keyword": "公园",
  "domainId": "",
  "openLevel": "",
  "orderBy": "update_date DESC",
  "tradeType": "",
  "sourceType": "",
  "createDate": "",
  "updateDate": "",
  "subjectType": "",
  "sourceSuffix": "",
  "serviceType": "",
  "updateFrequency": "",
  "officecCode": ""
}