name=AutoDevZh

autodev.chat=聊天

chat.panel.send=发送
chat.panel.newSketch=新建 Sketch
chat.panel.initial.text='Enter' 发送，'Shift+Enter' 开启新行, '/' 调用 DevIns 命令，'@' 调用 Agent
chat.too.long.user.message=消息长度太长，包含{0}个 Token
chat.input.tips=内容不能为空

intention.category.llm=AutoDev
intentions.write.action=生成代码
intentions.assistant.name=AutoDev AI 自动 Action
intentions.assistant.popup.title=AutoDev AI 助手

intentions.chat.code.test.step.prepare-context=准备上下文中
intentions.chat.code.test.step.collect-context=收集所需提示词与上下文
intentions.chat.code.complete.name=代码补全
intentions.chat.code.complete.family.name=代码补全
intentions.chat.code.test.name=测试此代码
intentions.chat.code.test.family.name=测试此代码
intentions.chat.new.family.name=带代码的新聊天
intentions.chat.selected.code.name=基于此代码聊天
intentions.chat.selected.fragment.name=与 {0} 个代码段聊天
intentions.chat.selected.element.name=与 ''{0}'' {1} 聊天
intentions.living.documentation.name=生成文档
intentions.living.documentation.family.name=生成文档
intentions.request.background.process.title=您的 LLM 正在处理您的请求

autodev.custom.prompt.placeholder=在此处自定义提示词
autodev.custom.intentions.family=自定义意图

# 请勿删除以下行，也不要重命名它们，除非更改 [LLMSettingCompoent] 类
settings.languageParam=语言
settings.gitTypeParam=Git 类型
settings.gitLabUrlParam=Gitlab 服务器 URL
settings.gitLabTokenParam=Gitlab 令牌
settings.gitHubTokenParam=GitHub 令牌
settings.maxTokenLengthParam=最大 token 长度
settings.customEngineServerParam=LLM 服务器 URL
settings.customModelParam=模型名称
settings.customEngineTokenParam=LLM 服务器密钥
settings.delaySecondsParam=请求延迟秒数（可选）
settings.customEngineResponseFormatParam=自定义响应格式（Json 路径）
settings.customEngineResponseTypeParam=自定义响应类型
settings.customEngineRequestBodyFormatParam=自定义请求主体格式（Json）
settings.customEngineRequestHeaderFormatParam=自定义请求头格式（Json）

settings.customize.title=智能体自定义
counit.agent.enable.label=启用自定义智能体（实验性）
counit.agent.json.placeholder=自定义智能体 JSON 配置

action.new.genius.cicd.github=生成 GitHub Actions

settings.external.team.prompts.path=团队 AI 路径

settings.autodev.coder=高级设置
settings.autodev.coder.recordingInLocal=本地记录 AI 请求和响应（Instruction）
settings.autodev.coder.trimCodeBeforeSend=在发送代码之前修剪代码
settings.autodev.coder.disableAdvanceContext=禁用高级上下文
settings.autodev.coder.disableAdvanceContext.tips=如框架上下文、语言上下文等
settings.autodev.coder.inEditorCompletion=编辑器中的实时补全
settings.autodev.coder.noChatHistory=没有聊天记录
settings.autodev.coder.enableExportAsMcpServer=允许作为 MCP 服务器输出
settings.autodev.coder.enableObserver=使用 Observer 模式
settings.autodev.coder.customActions=自定义提示词 Action（Json）：

settings.autodev.devops=DevOps (SDLC)

# 状态栏
autodev.statusbar.name=AutoDev 状态栏
autodev.statusbar.popup.title=AutoDev 状态栏
autodev.statusbar.id=autodev.statusBarPopup

# PL/SQL
migration.database.plsql=PL/SQL 迁移
migration.database.plsql.generate.function=生成函数
migration.database.plsql.generate.unittest=生成单元测试
migration.database.plsql.generate.entity=生成实体

# 自动 SQL
autosql.name=AutoSQL
autosql.generate=生成 SQL
autosql.generate.clarify=澄清需求
autosql.generate.generate=生成 SQL

# 自动页面
autopage.generate=前端生成
autopage.generate.name=AutoPage 生成
autopage.generate.clarify=澄清需求
autopage.generate.design=设计页面

# Inlay
intentions.chat.inlay.complete.name=补全代码(Inlay 模式)
intentions.chat.inlay.complete.family.name =补全代码(Inlay 模式)
progress.run.task=正在运行任务

# 右键
settings.autodev.rightClick.explain=解释此处
settings.autodev.rightClick.refactor=重构此处
settings.autodev.rightClick.fixThis=修复此处
settings.autodev.rightClick.chat=讨论此处
settings.autodev.rightClick.genApiTest=生成 API 测试

# others
settings.autodev.others.fixThis=修复此问题 (AutoDev)
settings.autodev.others.quickAssistant=文本生成代码 (AutoDev)
settings.autodev.others.commitMessage=提交消息 (AutoDev)
settings.autodev.others.generateReleaseNote=生成发布说明 (AutoDev)
settings.autodev.others.codeReview=代码审查 (AutoDev)
settings.autodev.others.codeComplete=补全代码 (AutoDev)
settings.autodev.others.editSettings=编辑设置

# simple prompts
prompts.autodev.explainCode=解释如下 {0} 代码。
prompts.autodev.refactorCode=重构给定的 {0} 代码。
prompts.autodev.completeCode=完成 {0} 代码，返回剩余代码，不解释。
prompts.autodev.generateTest=为 {0} 代码生成测试。
prompts.autodev.fixProblem=帮我修复问题：
prompts.autodev.generateReleaseNote=生成发布说明
prompts.autodev.generateTestData=基于给定的 {0} 代码和请求/响应信息生成 API 测试请求（使用 markdown 代码块）。这样我们就可以用它来测试 API
settings.autodev.coder.enableRenameSuggestion=启用重命名建议
settings.autodev.coder.enableAutoRepairDiff=启用自动 Apply 和修复 Diff
settings.autodev.coder.enableAutoRunTerminal=启用自动运行终端（有风险）
settings.autodev.coder.enableAutoLintCode=启用自动修复 Lint 代码
settings.autodev.coder.enableRenderWebview=在聊天页中启用渲染 WebView
settings.autodev.coder.enableAutoScrollInSketch=在 Sketch 中启用自动滚动
settings.autodev.coder.enableDiffViewer=在 Sketch 中启用 Diff 视图
shell.command.suggestion.action.default.text=如何创建一个新的分支?
batch.nothing.to.testing=没有要 AutoTest 的内容
intentions.chat.code.test.verify=验证测试中
custom.agent.open.documents=打开文档
settings.autodev.coder.testConnectionButton.tips=请记得在修改后点击应用，再进行测试！

sketch.patch.action.accept=Accept
sketch.patch.action.accept.tooltip=Accept the change
sketch.patch.action.reject=Reject
sketch.patch.action.reject.tooltip=Reject the change
sketch.patch.action.viewDiff.tooltip=查看变更
sketch.patch.action.applyDiff.tooltip=采纳 Diff
sketch.patch.action.repairDiff.tooltip=修复 Diff（使用 FastApply）
# rollback
prompts.autodev.inlineChat=According user selection to ask user question
prompts.autodev.sketch=Sketch
sketch.composer.mode=Composer Mode（自动模式）
sketch.lint.error.tooltip=查看 Lint/Inspection 错误
sketch.lint.error=发现 {0} 个 Lint/Inspection 错误
custom.action=Custom Action
custom.living.documentation=Custom Living Documentation
sketch.dependencies.check=Check dependencies has Issues
prompts.autodev.bridge=Bridge
autodev.custom.llms.placeholder=自定义不同 LLM
settings.autodev.coder.customLlms=自定义不同模型: Plan/ACT/Completion/Embedding/FastApply
sketch.compile.devins=收集上下文中（即转译 AutoDev DevIns 指令）
autodev.run.action=Run this file
llm.error.url.scheme=请请请先配置好您的模型，参考文档：https://ide.unitmesh.cc/quick-start
counit.mcp.services.placeholder=配置 MCP Servers
sketch.plan.finish.task=请帮我完成以下任务:
sketch.plan.review=AI 重新评估 Sketch 计划
sketch.plan.edit=添加/编辑计划
sketch.plan.reviewing=审阅计划中...
sketch.write.to.file=写入文件
sketch.plan.empty=最后的计划是空的
settings.autodev.coder.requires.restart=需要重启 IDE
sketch.terminal.execute=执行 Shell
sketch.terminal.copy.text=复制文件
sketch.terminal.send.chat=发送到聊天
sketch.terminal.popup=弹窗 Terminal
autodev.insert.action=添加到输入框中
autodev.copy.action=复制
autodev.save.action=保存文件
sketch.patch.repaired=已修复
sketch.patch.repair=修复
sketch.patch.apply=采纳
sketch.patch.view=查看
sketch.diff.original=原始代码
sketch.diff.aiSuggestion=AI 建议
sketch.patch.failed.read=读取文件失败: {0}
sketch.patch.failed.apply=应用补丁失败: {0}
sketch.patch.document.null=文件 {0} 的文档为空
autodev.save.as.file=保存文件
autodev.save.as.file.description=选择待保存文件位置
sketch.patch.regenerate=再生成
sketch.patch.action.regenerate.tooltip=重新生成代码
sketch.terminal.show.hide=显示或隐藏终端

chat.panel.stop=停止
chat.panel.enhance=丰富提示词
chat.panel.clear.all=清空
chat.panel.clear.all.tooltip=清除所有文件
chat.panel.select.files=请双击选择文件，以放到输入框内（相关文件）
sketch.plan.create=创建问题 issue
planner.stats.changes.empty=没有变更
planner.change.list.title=变更列表
planner.action.discard.all=丢弃全部
planner.action.accept.all=接受全部
planner.stats.no.changes=没有变更
planner.no.code.changes=没有代码变更
planner.stats.changes.count= (共 {0} 个文件变更)
planner.action.view.changes=查看变更
planner.action.discard.changes=丢弃变更
planner.action.accept.changes=Accept
planner.error.no.file=没有文件 {0}
planner.error.no.after.file=未找到变更后的文件路径
planner.error.create.file=Create file error {0}
sketch.plan.rerun.task=Help me fix this failed task:
sketch.terminal.stop=停止 Terminal
sketch.mcp.testMcp=测试 MCP 服务
sketch.mcp.services.docs=MCP 文档
sketch.issue.input.placeholder=请输入需求、问题描述
sketch.issue.input.submit=提交
sketch.issue.input.cancel=取消
planner.task.status.completed=已完成
planner.task.status.failed=已失败
planner.task.status.in_progress=进行中
planner.task.status.todo=待办
planner.task.execute=执行
chat.panel.workspace.files=工作区文件
chat.panel.add.files.tooltip=添加文件到工作区
chat.panel.select.files.title=选择工作区文件
chat.panel.select.files.description=选择要添加到工作区的文件
chat.panel.remove.file.tooltip=从工作区移除文件
chat.panel.add.openFiles=添加所有打开的文件

indexer.generate.domain=生成 domain.csv

# MCP Chat Config Dialog
mcp.chat.config.dialog.title=模型配置
mcp.chat.config.dialog.temperature=温度: {0}
mcp.chat.config.dialog.enabled.tools=已启用工具
# MCP Chat Result Panel
mcp.chat.result.tab.response=响应
mcp.chat.result.tab.tools=工具
mcp.chat.result.no.tools=响应中未找到工具调用
mcp.chat.result.execute=执行
mcp.chat.result.executing=正在执行工具 {0}...
mcp.chat.result.error.tool.not.found=错误：找不到匹配的工具 ''{0}''
mcp.chat.result.execution.time=耗时
mcp.chat.result.execute.all=执行所有工具
mcp.chat.result.tab.messages=消息日志
# MCP Tool Detail Dialog
mcp.tool.detail.dialog.title=MCP 工具详情 - {0}
mcp.tool.detail.dialog.from.server=来自服务: {0}
mcp.tool.detail.dialog.no.description=暂无描述
mcp.tool.detail.dialog.parameters=参数
mcp.tool.detail.dialog.verify=验证（自动生成）
mcp.tool.detail.dialog.result=结果
mcp.tool.detail.dialog.execute=执行
# McpPreviewEditor
mcp.preview.editor.title=MCP 工具
mcp.preview.editor.search.placeholder=搜索工具...
mcp.preview.editor.model.label=模型
mcp.preview.editor.configure.button=配置
mcp.preview.editor.test.button.tooltip=测试调用工具
mcp.preview.editor.empty.message.warning=请输入要发送的消息
mcp.preview.editor.loading.response=加载响应中...
# McpFileEditorWithPreview
mcp.editor.preview.title=预览
mcp.editor.preview.tooltip=预览面板
mcp.editor.refresh.title=刷新
shire.toolchain.function.not.found=Toolchain Not Found {0}
shire.run.local.mode=Run Local Model
devins.llm.notfound=LLM not found
devins.llm.done=Done
intentions.step.prepare-context=Prepare Context
devins.intention=Shire intention action
devins.newFile=New File
devins.file=DevIns File
action.view.history.text=View History
action.view.history.description=View conversation history
popup.title.session.history=History
